from behave import given, when, then

from utils.ai_execute_util import AiExecuteUtil
from utils.assert_util import *
from utils.data_util import *
from utils.mock_utils import buryingpoint_setmockrule
from loguru import logger


@given('mock规则: {mock_rule}')
def set_mock_rule(context, mock_rule):
    buryingpoint_setmockrule(mock_rule, context.platform)


@given('更新埋点数据: {data}')
def update_extra_data(context, data):
    """
    更新埋点数据, 若已有则覆盖
    :param context:
    :param data: 提供字典类型的字符串, eg: {"quality": 32", "auto_play": 1}
    :return:
    """
    context.vt_expected_data_object.update_vt_expect_data(**eval(data))


@given('打开app')
def start_app(context):
    operator = context.operator
    operator.start_app()


@when('等待{seconds}秒')
def step_impl(context, seconds):
    time.sleep(float(seconds))


@when('观看{seconds}秒')
def step_impl(context, seconds):
    context.played_time = int(seconds)
    time.sleep(float(seconds))


@when('AI执行用例步骤, desc: {desc}')
def ai_execute_case(context, desc):
    ai_executor: AiExecuteUtil = context.ai_executor
    ai_executor.execute_testcase_by_desc(desc)


@then('播放开始时上报断言vt')
def step_impl(context):
    platform = context.platform
    # 拿到期望数据
    vt_expected_data_object = context.vt_expected_data_object
    expected_data = vt_expected_data_object.get_vt_expect_data()

    # 获取实际数据并校验
    actual_data = assert_vt_play_start_data(vt_expected_data_object)
    logger.info(f"播放开始时上报断言成功, actual_data: {actual_data}")
    # 从实际值中拿出起始值, 并且直接更新到期望数据中
    vt_expected_data_object.update_expect_play_time_data(
        played_time=actual_data['played_time'],
        total_time=actual_data['total_time'],
        actual_played_time=actual_data['actual_played_time'],
        last_play_progress_time=actual_data['last_play_progress_time'],
        max_play_progress_time=actual_data['max_play_progress_time']
    )


@then('播放结束时上报断言vt')
def step_impl(context):
    vt_expect_data_object = context.vt_expected_data_object
    # 校验数据
    assert_vt_play_end_data(vt_expect_data_object)


@then('切换ep上报断言')
def step_impl(context):
    vt_expect_data_object = context.vt_expected_data_object
    assert_vt_switch_ep_data(vt_expect_data_object)



@then("播放开始时上报断言vv")
def step_impl(context):
    vv_expect_data_object = context.vv_expected_data_object
    assert_vv_data(vv_expect_data_object)



if __name__ == '__main__':
    vt_expected_data_object = VTExpectedData("android")

    logger.info(f"初始期望数据:{json.dumps(vt_expected_data_object.get_vt_expect_data())}")

    logger.info("进入视频, 等待 20 s")
    time.sleep(20)

    logger.info("校验播放开始数据")
    # 获取实际数据并校验
    actual_data = assert_vt_play_start_data(vt_expected_data_object)
    logger.info(f"播放开始时上报断言成功, actual_data: {actual_data}")
    # 从实际值中拿出起始值, 并且直接更新到期望数据中
    vt_expected_data_object.update_expect_play_time_data(
        played_time=actual_data['played_time'],
        total_time=actual_data['total_time'],
        actual_played_time=actual_data['actual_played_time'],
        last_play_progress_time=actual_data['last_play_progress_time'],
        max_play_progress_time=actual_data['max_play_progress_time']
    )

    logger.info("退出视频, 等待 20 s")
    time.sleep(20)
    logger.info("校验播放结束数据")
    assert_vt_play_end_data(vt_expected_data_object)


